// @ts-check

/**
 * OpenAI GPT-4.1 Translation Engine with JSON Mode
 *
 * This module provides an advanced translation system that utilizes OpenAI GPT-4.1
 * with JSON mode for intelligent anime subtitle translation.
 *
 * Features:
 * - Intelligent scene detection for dynamic chunking
 * - Screenshot analysis for visual context
 * - Iterative correction and improvement
 * - Context-aware translation with character consistency
 * - Polish language optimization
 * - JSON mode for structured responses
 */

import OpenAI from 'openai';
import { SceneDetector } from './tools/scene-detector.js';
import { ScreenshotTool } from './tools/screenshot-tool.js';
import { CorrectionTool } from './tools/correction-tool.js';
import { SecondLanguageValidator } from './tools/second-language-validator.js';
import { ExamplesReference } from './tools/examples-reference.js';
import { MetadataPersistence } from './tools/metadata-persistence.js';
import { ContextManager } from './context-manager.js';
import { getValidatedConfig, PROMPTS } from './config.js';
import fs from 'fs';
import path from 'path';

// Color constants for better log readability
const COLORS = {
  RESET: '\x1b[0m',
  BRIGHT: '\x1b[1m',
  DIM: '\x1b[2m',

  // Text colors
  RED: '\x1b[31m',
  GREEN: '\x1b[32m',
  YELLOW: '\x1b[33m',
  BLUE: '\x1b[34m',
  MAGENTA: '\x1b[35m',
  CYAN: '\x1b[36m',
  WHITE: '\x1b[37m',
  GRAY: '\x1b[90m',

  // Background colors
  BG_RED: '\x1b[41m',
  BG_GREEN: '\x1b[42m',
  BG_YELLOW: '\x1b[43m',
  BG_BLUE: '\x1b[44m',
  BG_MAGENTA: '\x1b[45m',
  BG_CYAN: '\x1b[46m',

  // Combinations for specific use cases
  SUCCESS: '\x1b[32m\x1b[1m',      // Bright green
  ERROR: '\x1b[31m\x1b[1m',        // Bright red
  WARNING: '\x1b[33m\x1b[1m',      // Bright yellow
  INFO: '\x1b[36m',                // Cyan
  DEBUG: '\x1b[90m',               // Gray
  HIGHLIGHT: '\x1b[35m\x1b[1m',    // Bright magenta
  SCENE: '\x1b[34m\x1b[1m',        // Bright blue
  QUALITY: '\x1b[32m',             // Green
  TRANSLATION: '\x1b[37m\x1b[1m'   // Bright white
};

export class OpenAITranslator {
  constructor(options = {}) {
    // Load and merge configuration
    const config = getValidatedConfig(process.env.NODE_ENV || 'production');
    this.config = { ...config, ...options };

    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });

    this.model = this.config.model;
    this.maxTokens = this.config.maxTokens;
    this.temperature = this.config.temperature;

    // Initialize tools with configuration
    this.sceneDetector = new SceneDetector(this.config.sceneDetection);
    this.screenshotTool = new ScreenshotTool(this.config.screenshot);
    this.correctionTool = new CorrectionTool(this.config.correction);
    this.secondLanguageValidator = new SecondLanguageValidator(this.config.secondLanguageValidation || {});
    this.examplesReference = new ExamplesReference(this.config.examplesReference || {});
    this.metadataPersistence = new MetadataPersistence(this.config.metadataPersistence || {});
    this.contextManager = new ContextManager();

    // Translation settings
    this.maxRetries = this.config.maxRetries;
    this.enableScreenshots = this.config.enableScreenshots;
    this.enableCorrection = this.config.enableCorrection;

    console.log(`${COLORS.SUCCESS}🤖 [OpenAITranslator] Initialized with model: ${this.model}${COLORS.RESET}`);

    // Setup graceful shutdown
    process.on('SIGINT', () => this.shutdown());
    process.on('SIGTERM', () => this.shutdown());
  }

  /**
   * Graceful shutdown - save metadata and cleanup
   */
  async shutdown() {
    console.log(`${COLORS.INFO}🔄 [OpenAITranslator] Shutting down gracefully...${COLORS.RESET}`);

    try {
      await this.metadataPersistence.shutdown();
      console.log(`${COLORS.SUCCESS}✅ [OpenAITranslator] Shutdown completed${COLORS.RESET}`);
    } catch (error) {
      console.error(`${COLORS.ERROR}❌ [OpenAITranslator] Error during shutdown: ${error.message}${COLORS.RESET}`);
    }

    process.exit(0);
  }

  /**
   * Main translation method that processes subtitle content with intelligent chunking
   * @param {string} subtitleContent - Raw subtitle content
   * @param {string|null} videoPath - Path to video file for screenshots (optional)
   * @param {Object} metadata - Anime metadata (title, characters, genres)
   * @param {string} fileName - Current file name for second language lookup
   * @returns {Promise<string>} - Translated content
   */
  async translateSubtitles(subtitleContent, videoPath = null, metadata = {}, fileName = '') {
    try {
      console.log(`${COLORS.INFO}🚀 [OpenAITranslator] Starting translation process with GPT-4.1...${COLORS.RESET}`);

      // Store current file name for tools
      this.currentFileName = fileName;

      // Initialize context with metadata
      this.contextManager.initialize(metadata);

      // Load persistent metadata for this anime
      if (metadata.title) {
        const persistedMetadata = this.metadataPersistence.getAnimeMetadata(metadata.title, metadata);
        this.contextManager.initialize({ ...metadata, ...persistedMetadata });
      }

      // Detect scenes and create intelligent chunks
      const scenes = await this.sceneDetector.detectScenes(subtitleContent);
      console.log(`${COLORS.SCENE}📋 [OpenAITranslator] Detected ${scenes.length} scenes${COLORS.RESET}`);

      let translatedContent = '';

      for (let i = 0; i < scenes.length; i++) {
        const scene = scenes[i];
        console.log(`${COLORS.SCENE}🎬 [OpenAITranslator] Processing scene ${i + 1}/${scenes.length} (${scene.lines.length} lines)${COLORS.RESET}`);

        // Get visual context if enabled
        let visualContext = null;
        if (this.enableScreenshots && videoPath && scene.timestamp) {
          try {
            visualContext = await this.screenshotTool.captureFrame(videoPath, scene.timestamp);
            console.log(`${COLORS.INFO}📸 [OpenAITranslator] Screenshot captured for scene ${i + 1}${COLORS.RESET}`);
          } catch (error) {
            console.warn(`${COLORS.WARNING}⚠️  [OpenAITranslator] Screenshot failed for scene ${i + 1}: ${error.message}${COLORS.RESET}`);
          }
        }

        // Translate the scene
        const translatedScene = await this.translateScene(scene, visualContext, i);

        // Apply corrections and validation if enabled
        let finalScene = translatedScene;

        if (this.enableCorrection) {
          finalScene = await this.correctionTool.improveTranslation(
            scene.content,
            translatedScene,
            this.contextManager.getContext()
          );
        }

        // Validate with second language source if needed
        if (this.config.secondLanguageValidation?.enableValidation) {
          const validationResult = await this.secondLanguageValidator.validateTranslation(
            scene.content,
            finalScene,
            {
              fileName: this.currentFileName,
              animeTitle: this.contextManager.animeMetadata?.title,
              episode: this.contextManager.animeMetadata?.episode,
              sceneType: scene.emotionalTone
            }
          );

          if (!validationResult.isValid && validationResult.improvedTranslation) {
            console.log(`${COLORS.WARNING}🔄 [OpenAITranslator] Using improved translation from second language validation${COLORS.RESET}`);
            console.log(`${COLORS.IMPROVEMENT}📝 New translation:${COLORS.RESET}`);
            console.log(`${COLORS.IMPROVEMENT}${validationResult.improvedTranslation}${COLORS.RESET}`);
            finalScene = validationResult.improvedTranslation;
          }
        }

        translatedContent += finalScene + '\n';

        // Update context with translated content
        this.contextManager.updateContext(scene, finalScene);
      }

      // Save metadata after translation
      if (metadata.title && metadata.episode) {
        this.metadataPersistence.addEpisodeData(metadata.title, metadata.episode, {
          scenes: scenes.length,
          lines: scenes.reduce((sum, scene) => sum + scene.lines.length, 0),
          quality: 0.8, // Default quality score
          characters: [...new Set(scenes.flatMap(scene => scene.speakers))],
          translatedAt: new Date().toISOString()
        });
      }

      console.log(`${COLORS.SUCCESS}✅ [OpenAITranslator] Translation completed successfully!${COLORS.RESET}`);
      return translatedContent.trim();

    } catch (error) {
      console.error(`${COLORS.ERROR}❌ [OpenAITranslator] Translation failed: ${error.message}${COLORS.RESET}`);
      throw error;
    }
  }

  /**
   * Translate a single scene using OpenAI GPT-4.1 with JSON mode
   * @param {Object} scene - Scene object with content and metadata
   * @param {Object|null} visualContext - Screenshot data if available
   * @param {number} sceneIndex - Index of current scene
   * @returns {Promise<string>} - Translated scene content
   */
  async translateScene(scene, visualContext, sceneIndex) {
    const userPrompt = this.buildUserPrompt(scene, visualContext, sceneIndex);

    for (let attempt = 1; attempt <= this.maxRetries; attempt++) {
      try {
        console.log(`${COLORS.INFO}🔄 [OpenAITranslator] Requesting translation from GPT-4.1... (attempt ${attempt}/${this.maxRetries})${COLORS.RESET}`);
        const response = await this.openai.chat.completions.create({
          model: this.model,
          max_tokens: this.maxTokens,
          temperature: this.temperature,
          response_format: this.config.responseFormat,
          messages: [
            {
              role: 'system',
              content: PROMPTS.translation
            },
            {
              role: 'user',
              content: userPrompt
            }
          ]
        });

        // Process the JSON response
        return await this.processOpenAIResponse(response, scene);

      } catch (error) {
        console.warn(`${COLORS.WARNING}⚠️  [OpenAITranslator] GPT-4.1 translation attempt ${attempt} failed: ${error.message}${COLORS.RESET}`);
        if (attempt === this.maxRetries) {
          console.error(`${COLORS.ERROR}❌ [OpenAITranslator] All translation attempts failed, returning original content${COLORS.RESET}`);
          return scene.content; // Return original content as fallback
        }
        console.log(`${COLORS.DEBUG}⏳ [OpenAITranslator] Retrying in ${2 * attempt} seconds...${COLORS.RESET}`);
        await this.sleep(2000 * attempt); // Exponential backoff
      }
    }

    // This should never be reached, but included for safety
    return scene.content;
  }

  /**
   * Process OpenAI's JSON response
   * @param {Object} response - OpenAI's response
   * @param {Object} scene - Original scene
   * @returns {Promise<string>} - Final translated content
   */
  async processOpenAIResponse(response, scene) {
    console.log(`${COLORS.DEBUG}🔍 [OpenAITranslator] Processing OpenAI JSON response...${COLORS.RESET}`);

    try {
      const content = response.choices[0]?.message?.content;
      if (!content) {
        console.warn(`${COLORS.WARNING}⚠️  [OpenAITranslator] No content in response${COLORS.RESET}`);
        return scene.content;
      }

      const jsonResponse = JSON.parse(content);
      console.log(`${COLORS.DEBUG}📋 [OpenAITranslator] Parsed JSON response:${COLORS.RESET}`);
      console.log(`${COLORS.DEBUG}${JSON.stringify(jsonResponse, null, 2)}${COLORS.RESET}`);

      // Extract translations from JSON response
      if (jsonResponse.translations && Array.isArray(jsonResponse.translations)) {
        return this.formatTranslations(jsonResponse.translations);
      } else if (jsonResponse.translation) {
        // Handle single translation format
        return jsonResponse.translation;
      } else {
        console.warn(`${COLORS.WARNING}⚠️  [OpenAITranslator] Unexpected JSON format${COLORS.RESET}`);
        return scene.content;
      }

    } catch (error) {
      console.error(`${COLORS.ERROR}❌ [OpenAITranslator] Failed to parse JSON response: ${error.message}${COLORS.RESET}`);
      console.error(`${COLORS.DEBUG}📄 Raw content: ${response.choices[0]?.message?.content}${COLORS.RESET}`);
      return scene.content;
    }
  }

  buildUserPrompt(scene, visualContext, sceneIndex) {
    let prompt = `Please translate this anime scene (Scene ${sceneIndex + 1}) from English to Polish.

SCENE CONTENT:
${scene.content}

SCENE METADATA:
- Timestamp: ${scene.timestamp || 'Unknown'}
- Duration: ${scene.duration || 'Unknown'}
- Line count: ${scene.lines.length}

CONTEXT:
${this.contextManager.getContextSummary()}`;

    if (visualContext) {
      prompt += `\n\nVISUAL CONTEXT:
A screenshot has been captured at timestamp ${scene.timestamp} to provide visual context for this scene.`;
    }

    prompt += `\n\nRespond with a JSON object in this format:
{
  "translations": [
    {
      "speaker": "Character Name",
      "original": "Original English line",
      "translation": "Polish translation",
      "notes": "Optional translation notes"
    }
  ]
}`;

    return prompt;
  }



  /**
   * Format translations into final output
   * @param {Array|any} translations - Array of translation objects or other format
   * @returns {string} - Formatted translation content
   */
  formatTranslations(translations) {
    console.log(`${COLORS.DEBUG}🔧 [OpenAITranslator] Formatting translations...${COLORS.RESET}`);

    // Handle case where translations is not an array
    if (!Array.isArray(translations)) {
      console.log(`${COLORS.INFO}📝 [OpenAITranslator] Input is ${typeof translations}, attempting to convert to array format${COLORS.RESET}`);

      // If it's a string, try to parse it as JSON first
      if (typeof translations === 'string') {
        console.log(`${COLORS.DEBUG}🔍 [OpenAITranslator] Attempting to parse string as JSON...${COLORS.RESET}`);
        try {
          const parsed = JSON.parse(translations);
          if (Array.isArray(parsed)) {
            console.log(`${COLORS.SUCCESS}✅ [OpenAITranslator] Successfully parsed JSON array with ${parsed.length} items${COLORS.RESET}`);
            return this.formatTranslations(parsed);
          } else if (parsed && typeof parsed === 'object') {
            console.log(`${COLORS.INFO}📝 [OpenAITranslator] Parsed JSON object, checking for translation properties${COLORS.RESET}`);
            if (parsed.translation && parsed.speaker) {
              console.log(`${COLORS.SUCCESS}✅ [OpenAITranslator] Converting single object to array format${COLORS.RESET}`);
              return this.formatTranslations([parsed]);
            }
          }
        } catch (error) {
          console.log(`${COLORS.DEBUG}🔍 [OpenAITranslator] String is not valid JSON, treating as plain text${COLORS.RESET}`);
        }

        console.log(`${COLORS.INFO}📝 [OpenAITranslator] Returning string translation directly${COLORS.RESET}`);
        return translations;
      }

      // If it's an object with a single translation, try to extract it
      if (typeof translations === 'object' && translations !== null) {
        if (translations.translation && translations.speaker) {
          console.log(`${COLORS.SUCCESS}✅ [OpenAITranslator] Converting single object to formatted string${COLORS.RESET}`);
          return `${translations.speaker}: ${translations.translation}`;
        }

        if (translations.translations && Array.isArray(translations.translations)) {
          console.log(`${COLORS.SUCCESS}✅ [OpenAITranslator] Found nested translations array${COLORS.RESET}`);
          return this.formatTranslations(translations.translations);
        }

        console.log(`${COLORS.INFO}📝 [OpenAITranslator] Converting object to JSON string${COLORS.RESET}`);
        return JSON.stringify(translations, null, 2);
      }

      console.log(`${COLORS.INFO}📝 [OpenAITranslator] Converting to string as fallback${COLORS.RESET}`);
      return String(translations);
    }

    // Normal array processing
    console.log(`${COLORS.SUCCESS}✅ [OpenAITranslator] Processing ${translations.length} translation objects${COLORS.RESET}`);
    return translations
      .map(t => {
        if (t && t.speaker && t.translation) {
          return `${t.speaker}: ${t.translation}`;
        } else if (t && typeof t === 'object') {
          const speaker = t.speaker || t.character || t.actor || 'Unknown';
          const translation = t.translation || t.polish || t.text || String(t);
          console.log(`${COLORS.DEBUG}🔍 [OpenAITranslator] Extracted speaker: "${speaker}", translation: "${translation.substring(0, 50)}..."${COLORS.RESET}`);
          return `${speaker}: ${translation}`;
        } else {
          console.log(`${COLORS.DEBUG}🔍 [OpenAITranslator] Converting non-object item to string: ${JSON.stringify(t)}${COLORS.RESET}`);
          return String(t);
        }
      })
      .join('\n');
  }

  /**
   * Sleep utility
   * @param {number} ms - Milliseconds to sleep
   * @returns {Promise<void>}
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
