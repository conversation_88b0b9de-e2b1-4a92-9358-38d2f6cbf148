/**
 * Configuration for OpenAI GPT-4.1 Translation System
 *
 * This file contains all configurable parameters for the translation system.
 * Uses GPT-4.1-2025-04-14 for all translation, validation, and routing operations.
 * Modify these values to customize the behavior according to your needs.
 */

export const OPENAI_CONFIG = {
  // Model Configuration per Task
  models: {
    // Default model for all tasks (fallback)
    default: "gpt-4.1-2025-04-14",

    // Routing and decision making
    routing: "gpt-4.1-2025-04-14",

    // Main translation tasks
    translation: "gpt-4.1-2025-04-14",

    // Translation validation and quality assessment
    validation: "gpt-4.1-2025-04-14",

    // Translation correction and improvement
    correction: "gpt-4.1-2025-04-14",

    // Scene detection and analysis
    sceneDetection: "gpt-4.1-2025-04-14",

    // Screenshot analysis and visual context
    visualAnalysis: "gpt-4.1-2025-04-14",

    // Examples lookup and reference
    examplesAnalysis: "gpt-4.1-2025-04-14",

    // Second language validation
    secondLanguageValidation: "gpt-4.1-2025-04-14",

    // Metadata processing and persistence
    metadataProcessing: "gpt-4.1-2025-04-14"
  },

  // API Settings
  maxTokens: 8192,
  temperature: 1,
  maxRetries: 3,
  responseFormat: { type: "json_object" }, // Enable JSON mode for all responses

  // Model-specific settings (can override per task)
  modelSettings: {
    "gpt-4.1-2025-04-14": {
      maxTokens: 8192,
      temperature: 1,
      responseFormat: { type: "json_object" }
    },
    "gpt-4o": {
      maxTokens: 4096,
      temperature: 0.6,
      responseFormat: { type: "json_object" }
    },
    "gpt-4o-mini": {
      maxTokens: 4096,
      temperature: 0.5,
      responseFormat: { type: "json_object" }
    },
    "gpt-3.5-turbo": {
      maxTokens: 4096,
      temperature: 0.6,
      responseFormat: { type: "json_object" }
    }
  },

  // Translation Features
  enableScreenshots: true,
  enableCorrection: true,

  // Scene Detection Settings
  sceneDetection: {
    minSceneLength: 4,        // Minimum lines per scene
    maxSceneLength: 25,       // Maximum lines per scene (reduced for better processing)
    timingGapThreshold: 5000, // Timing gap threshold in milliseconds
    speakerChangeWeight: 0.3, // Weight for speaker changes (0-1)
    timingWeight: 0.4,        // Weight for timing gaps (0-1)
    contentWeight: 0.3,       // Weight for content transitions (0-1)
    qualityThreshold: 0.3,    // Minimum quality threshold for scene breaks
    enforceMaxLength: true,   // Enforce maximum scene length strictly
    enforceMinLength: true,   // Enforce minimum scene length by merging small scenes
    fallbackSplitThreshold: 30 // Force split scenes larger than this (fallback)
  },

  // Screenshot Settings
  screenshot: {
    outputDir: '2translate/screenshots',
    quality: 2,               // FFmpeg quality scale (1-31, lower is better)
    format: 'png',            // Output format (png, jpg)
    maxWidth: 1280,           // Maximum width for screenshots
    cleanupAge: 24 * 60 * 60 * 1000, // Cleanup age in milliseconds (24 hours)
    enableAnalysis: true,     // Enable image analysis
    captureSequence: false    // Capture multiple frames around timestamp
  },

  // Correction Settings
  correction: {
    maxIterations: 3,         // Maximum correction iterations (increased for better quality)
    qualityThreshold: 0.85,    // Quality threshold to stop iterations (0-1) - more strict
    enableGrammarCheck: true, // Enable Polish grammar checking
    enableConsistencyCheck: true, // Enable character consistency checking
    enableCulturalAdaptation: true, // Enable cultural adaptation

    // Quality weights for overall score calculation (focus on naturalness and accuracy)
    qualityWeights: {
      grammar: 0.20,
      naturalness: 0.35,
      accuracy: 0.35,
      consistency: 0.10,
      culturalAdaptation: 0.10
    }
  },

  // Second Language Validation
  secondLanguageValidation: {
    enableValidation: true,         // Enable second language validation
    validationThreshold: 0.6,       // Quality threshold for triggering validation
    secondLanguageDirectory: '2translate/toTranslate'
  },

  // Examples Reference
  examplesReference: {
    enableExampleLookup: true,      // Enable examples.xml lookup
    examplesPath: '2translate/examples.xml',
    maxRelevantExamples: 5          // Maximum examples to consider
  },

  // Metadata Persistence
  metadataPersistence: {
    enablePersistence: true,        // Enable metadata persistence
    metadataDirectory: '2translate/metadata',
    autoSave: true,                 // Auto-save metadata
    saveInterval: 30000             // Auto-save interval in milliseconds
  }
};

// Translation and Validation Prompts
export const PROMPTS = {
  translation: `You are a highly skilled translator and linguistic expert specializing in anime subtitles. Your task is to translate English into Polish while significantly improving the overall quality. You will preserve the core meaning and tone of the original texts while enhancing clarity, naturalness, and adherence to Polish language structure and word order.

Key Guidelines:
1. Use natural Polish word order and sentence structure, rephrasing for better flow and clarity
2. Maintain proper grammar, spelling, and punctuation according to Polish language rules
3. Improve awkward phrasing or unclear sentences while preserving core meaning
4. Enhance dialogue to sound more natural and in-character
5. Preserve Japanese honorifics like 'sama', 'chan', 'san' without inflecting them
6. Use Polish-specific structures, starting sentences with verbs when appropriate
7. Incorporate Polish particles (e.g., 'że', 'no', 'to') naturally to enhance flow
8. Adjust idioms and cultural references to resonate with Polish audiences while maintaining original intent
9. For slang and colloquial language, use appropriate Polish equivalents that maintain the same level of informality and cultural connotations
10. Never translate phrasal verbs or idiomatic expressions literally - analyze context and choose appropriate Polish equivalents
11. Polish is a pro-drop language - omit subject pronouns when verb conjugation indicates the subject
12. Use contextual understanding to interpret ambiguous phrases and enhance clarity

Always prioritize Polish language conventions and create translations that sound natural and engaging to Polish-speaking anime fans.

Respond with a JSON object containing your translations.`,

  validation: `You are a highly skilled Polish translation editor and quality assurance expert specializing in anime subtitles. Your task is to review and correct Polish translations from English, identifying and fixing issues while significantly improving the overall quality.

Your role is to:
1. Detect and correct translation errors, mistranslations, and awkward phrasing
2. Identify issues with Polish grammar, spelling, punctuation, and sentence structure
3. Improve naturalness and fluency while preserving the original meaning and tone
4. Ensure adherence to proper Polish language conventions and word order
5. Verify appropriate use of anime-specific terminology and Japanese honorifics

Key Quality Checks:
1. Grammar and syntax - Ensure proper Polish sentence structure and case usage
2. Naturalness - Make dialogue sound like native Polish speakers would actually say it
3. Consistency - Check for consistent terminology and character voice throughout
4. Cultural adaptation - Verify idioms and references work for Polish audiences
5. Honorifics preservation - Ensure Japanese honorifics like 'sama', 'chan', 'san' remain unchanged
6. Pronoun usage - Utilize Polish pro-drop features appropriately
7. Particle integration - Natural use of Polish particles ('że', 'no', 'to') for better flow
8. Slang appropriateness - Verify colloquial language matches the original tone and context
9. Phrasal verb handling - Confirm idiomatic expressions are properly localized, not literally translated
10. Clarity enhancement - Improve any unclear or ambiguous phrasing while maintaining core meaning

Focus on creating polished, professional translations that sound natural and engaging to Polish-speaking anime fans while maintaining the authenticity and spirit of the original content.

Respond with a JSON object containing your analysis and improvements.`
};

/**
 * Get model for specific task
 * @param {string} task - Task name (translation, validation, correction, etc.)
 * @param {Object} config - Configuration object
 * @returns {string} - Model name
 */
export function getModelForTask(task, config = OPENAI_CONFIG) {
  return config.models[task] || config.models.default;
}

/**
 * Get model settings for specific model
 * @param {string} modelName - Model name
 * @param {Object} config - Configuration object
 * @returns {Object} - Model settings
 */
export function getModelSettings(modelName, config = OPENAI_CONFIG) {
  return config.modelSettings[modelName] || {
    maxTokens: config.maxTokens,
    temperature: config.temperature,
    responseFormat: config.responseFormat
  };
}

/**
 * Get complete configuration for a specific task
 * @param {string} task - Task name
 * @param {Object} config - Configuration object
 * @returns {Object} - Complete task configuration
 */
export function getTaskConfig(task, config = OPENAI_CONFIG) {
  const model = getModelForTask(task, config);
  const settings = getModelSettings(model, config);

  return {
    model,
    ...settings,
    maxRetries: config.maxRetries
  };
}

/**
 * Available task types for model configuration
 */
export const TASK_TYPES = {
  ROUTING: 'routing',
  TRANSLATION: 'translation',
  VALIDATION: 'validation',
  CORRECTION: 'correction',
  SCENE_DETECTION: 'sceneDetection',
  VISUAL_ANALYSIS: 'visualAnalysis',
  EXAMPLES_ANALYSIS: 'examplesAnalysis',
  SECOND_LANGUAGE_VALIDATION: 'secondLanguageValidation',
  METADATA_PROCESSING: 'metadataProcessing'
};

// Environment-specific overrides (non-model settings only)
export function getConfig(environment = 'production') {
  const config = { ...OPENAI_CONFIG };

  switch (environment) {
    case 'development':
      config.maxRetries = 1;
      config.correction.maxIterations = 1;
      config.enableScreenshots = false; // Faster for development
      break;

    case 'testing':
      config.enableScreenshots = false;
      config.enableCorrection = false;
      config.maxRetries = 1;
      break;

    case 'production':
      // Use default production settings
      break;

    default:
      console.warn(`Unknown environment: ${environment}, using production config`);
  }

  return config;
}

// Validation function to ensure config is valid
export function validateConfig(config) {
  const errors = [];

  // Validate required API key (only in production)
  if (!process.env.ANTHROPIC_API_KEY && process.env.NODE_ENV === 'production') {
    errors.push('ANTHROPIC_API_KEY environment variable is required');
  }

  // Validate numeric ranges
  if (config.temperature < 0 || config.temperature > 1) {
    errors.push('temperature must be between 0 and 1');
  }

  if (config.sceneDetection.minSceneLength >= config.sceneDetection.maxSceneLength) {
    errors.push('minSceneLength must be less than maxSceneLength');
  }

  if (config.correction.qualityThreshold < 0 || config.correction.qualityThreshold > 1) {
    errors.push('qualityThreshold must be between 0 and 1');
  }

  // Validate weights sum to 1
  const weights = Object.values(config.correction.qualityWeights);
  const weightSum = weights.reduce((sum, weight) => sum + weight, 0);
  if (Math.abs(weightSum - 1.0) > 0.01) {
    errors.push('qualityWeights must sum to 1.0');
  }

  // Skip directory validation for now to avoid async issues
  // This can be added back later with proper async handling

  if (errors.length > 0) {
    throw new Error(`Configuration validation failed:\n${errors.join('\n')}`);
  }

  return true;
}

// Helper function to get environment-specific config with validation
export function getValidatedConfig(environment) {
  const config = getConfig(environment);
  validateConfig(config);
  return config;
}

// Export default config for convenience
export default CLAUDE4_CONFIG;
