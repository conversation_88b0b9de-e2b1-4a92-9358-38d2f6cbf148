import fs from 'fs';
import { parseString } from 'xml2js';

const convertXmlToJsonl = async () => {
    try {
        // Read the XML file
        const xmlData = fs.readFileSync('examples.xml', 'utf8');

        // Parse XML to JavaScript object
        const result = await new Promise((resolve, reject) => {
            parseString(xmlData, (err, result) => {
                if (err) reject(err);
                else resolve(result);
            });
        });

        // Extract examples and convert to JSONL format
        const examples = result.examples.example;
        const formattedExamples = examples.map(example => {
            const englishSource = example.English_Source[0];
            const idealOutput = example.ideal_output[0].replace(/\s+/g, ' ').trim();

            return JSON.stringify({
                messages: [
                    {
                        role: "system",
                        content: "You are a highly skilled Polish translation editor and quality assurance expert specializing in anime subtitles. Your task is to review and correct Polish translations from English, identifying and fixing issues while significantly improving the overall quality.\n\nYour role is to:\n1. Detect and correct translation errors, mistranslations, and awkward phrasing\n2. Identify issues with Polish grammar, spelling, punctuation, and sentence structure\n3. Improve naturalness and fluency while preserving the original meaning and tone\n4. Ensure adherence to proper Polish language conventions and word order\n5. Verify appropriate use of anime-specific terminology and Japanese honorifics\n\nKey Quality Checks:\n1. Grammar and syntax - Ensure proper Polish sentence structure and case usage\n2. Naturalness - Make dialogue sound like native Polish speakers would actually say it\n3. Consistency - Check for consistent terminology and character voice throughout\n4. Cultural adaptation - Verify idioms and references work for Polish audiences\n5. Honorifics preservation - Ensure Japanese honorifics like 'sama', 'chan', 'san' remain unchanged\n6. Pronoun usage - Utilize Polish pro-drop features appropriately\n7. Particle integration - Natural use of Polish particles ('że', 'no', 'to') for better flow\n8. Slang appropriateness - Verify colloquial language matches the original tone and context\n9. Phrasal verb handling - Confirm idiomatic expressions are properly localized, not literally translated\n10. Clarity enhancement - Improve any unclear or ambiguous phrasing while maintaining core meaning\n\nFocus on creating polished, professional translations that sound natural and engaging to Polish-speaking anime fans while maintaining the authenticity and spirit of the original content."
                    },
                    {
                        role: "user",
                        content: englishSource
                    },
                    {
                        role: "assistant",
                        content: idealOutput
                    }
                ]
            });
        });

        // Split dataset in half
        const splitPoint = Math.floor(formattedExamples.length * 0.7);
        const trainingData = formattedExamples.slice(0, splitPoint);
        const evaluationData = formattedExamples.slice(splitPoint);

        // Write training data file
        fs.writeFileSync('training_data.jsonl', trainingData.join('\n'));

        // Write evaluation data file
        fs.writeFileSync('evaluation_data.jsonl', evaluationData.join('\n'));

        console.log('Successfully converted XML to JSONL format!');
        console.log(`Generated ${trainingData.length} training examples`);
        console.log(`Generated ${evaluationData.length} evaluation examples`);
        console.log(`Total: ${formattedExamples.length} examples`);

    } catch (error) {
        console.error('Error converting XML to JSONL:', error);
    }
};

// Run the conversion
convertXmlToJsonl();