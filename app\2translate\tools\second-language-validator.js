/**
 * Second Language Validator Tool
 * 
 * This tool provides validation using a second language source when translations
 * appear nonsensical or out of place. It helps catch translation errors by
 * cross-referencing with alternative language sources.
 * 
 * Features:
 * - Detects potentially problematic translations
 * - Calls second language source for validation
 * - Provides alternative translation suggestions
 * - Maintains quality scoring for validation decisions
 */

import OpenAI from 'openai';
import fs from 'fs';
import path from 'path';

// Color constants for better log readability
const COLORS = {
  RESET: '\x1b[0m',
  GREEN: '\x1b[32m',
  YELLOW: '\x1b[33m',
  BLUE: '\x1b[34m',
  MAGENTA: '\x1b[35m',
  CYAN: '\x1b[36m',
  RED: '\x1b[31m',
  GRAY: '\x1b[90m',
  SUCCESS: '\x1b[32m\x1b[1m',
  WARNING: '\x1b[33m\x1b[1m',
  ERROR: '\x1b[31m\x1b[1m',
  INFO: '\x1b[36m',
  DEBUG: '\x1b[90m',
  VALIDATOR: '\x1b[35m\x1b[1m'
};

export class SecondLanguageValidator {
  constructor(options = {}) {
    this.openai = new OpenAI({
      apiKey: process.env.OPENAI_API_KEY,
    });

    // Use GPT-4.1 for both analysis and validation
    this.model = "gpt-4.1-2025-04-14";
    this.validationThreshold = options.validationThreshold || 0.6; // Threshold for triggering validation
    this.qualityTargetThreshold = options.qualityTargetThreshold || 0.85; // Target quality threshold (85%)
    this.maxValidationIterations = options.maxValidationIterations || 5; // Maximum validation iterations
    this.enableValidation = options.enableValidation !== false;
    this.secondLanguageDirectory = options.secondLanguageDirectory || 'app/2translate/toTranslate';

    // Quality indicators that suggest problematic translations
    this.problemIndicators = [
      'nonsensical',
      'awkward',
      'unclear',
      'confusing',
      'literal',
      'unnatural',
      'grammatically incorrect',
      'culturally inappropriate'
    ];

    console.log(`${COLORS.SUCCESS}🔍 [SecondLanguageValidator] Initialized with OpenAI GPT-4.1:${COLORS.RESET}`);
    console.log(`${COLORS.INFO}   🤖 Model: ${this.model}${COLORS.RESET}`);
  }

  /**
   * Validate translation quality and trigger second language check if needed
   * @param {string} originalText - Original English text
   * @param {string} polishTranslation - Polish translation to validate
   * @param {Object} context - Translation context
   * @returns {Promise<Object>} - Validation result with suggestions
   */
  async validateTranslation(originalText, polishTranslation, context = {}) {
    try {
      console.log(`${COLORS.VALIDATOR}🔍 [SecondLanguageValidator] Starting validation process...${COLORS.RESET}`);

      let currentTranslation = polishTranslation;
      let iteration = 0;

      // First, assess if the translation needs validation
      let qualityAssessment = await this.assessTranslationQuality(originalText, currentTranslation, context);

      if (qualityAssessment.needsValidation) {
        console.log(`${COLORS.WARNING}⚠️  [SecondLanguageValidator] Translation flagged for validation: ${qualityAssessment.reason}${COLORS.RESET}`);

        // Try to find second language source
        const secondLanguageText = await this.findSecondLanguageSource(context.fileName);

        if (secondLanguageText) {
          console.log(`${COLORS.INFO}📚 [SecondLanguageValidator] Found second language source, performing iterative cross-validation...${COLORS.RESET}`);

          // Iterative validation loop
          while (iteration < this.maxValidationIterations) {
            iteration++;
            console.log(`${COLORS.INFO}🔄 [SecondLanguageValidator] Validation iteration ${iteration}/${this.maxValidationIterations}${COLORS.RESET}`);

            const validationResult = await this.performCrossValidation(originalText, currentTranslation, secondLanguageText, context);

            // Check if we've reached our quality target
            const hasHighPrioritySuggestions = this.hasHighPrioritySuggestions(validationResult);
            const qualityMeetsTarget = validationResult.confidence >= this.qualityTargetThreshold;

            console.log(`${COLORS.INFO}📊 [SecondLanguageValidator] Iteration ${iteration} results:${COLORS.RESET}`);
            console.log(`${COLORS.INFO}   Quality: ${(validationResult.confidence * 100).toFixed(1)}% (target: ${(this.qualityTargetThreshold * 100).toFixed(0)}%)${COLORS.RESET}`);
            console.log(`${COLORS.INFO}   High priority issues: ${hasHighPrioritySuggestions ? 'YES' : 'NO'}${COLORS.RESET}`);
            console.log(`${COLORS.INFO}   Issues found: ${validationResult.issues?.length || 0}${COLORS.RESET}`);

            if (qualityMeetsTarget && !hasHighPrioritySuggestions) {
              console.log(`${COLORS.SUCCESS}✅ [SecondLanguageValidator] Quality target reached (${(validationResult.confidence * 100).toFixed(1)}%) with no high priority issues${COLORS.RESET}`);
              return {
                ...validationResult,
                iterationsUsed: iteration,
                finalQuality: validationResult.confidence
              };
            }

            // If we have an improved translation, use it for the next iteration
            if (validationResult.improvedTranslation && validationResult.improvedTranslation !== currentTranslation) {
              console.log(`${COLORS.SUCCESS}✨ [SecondLanguageValidator] Using improved translation for next iteration${COLORS.RESET}`);
              currentTranslation = validationResult.improvedTranslation;
            } else if (iteration < this.maxValidationIterations) {
              console.log(`${COLORS.WARNING}⚠️  [SecondLanguageValidator] No improvement found, continuing with current translation${COLORS.RESET}`);
            }

            // If this is the last iteration, return the result
            if (iteration === this.maxValidationIterations) {
              console.log(`${COLORS.WARNING}⚠️  [SecondLanguageValidator] Maximum iterations (${this.maxValidationIterations}) reached${COLORS.RESET}`);
              return {
                ...validationResult,
                iterationsUsed: iteration,
                finalQuality: validationResult.confidence,
                maxIterationsReached: true
              };
            }
          }
        } else {
          console.log(`${COLORS.WARNING}⚠️  [SecondLanguageValidator] No second language source found, using quality assessment only${COLORS.RESET}`);
          return {
            isValid: qualityAssessment.score >= this.validationThreshold,
            confidence: qualityAssessment.score,
            issues: qualityAssessment.issues,
            suggestions: qualityAssessment.suggestions,
            usedSecondLanguage: false
          };
        }
      } else {
        console.log(`${COLORS.SUCCESS}✅ [SecondLanguageValidator] Translation passed initial quality check${COLORS.RESET}`);
        return {
          isValid: true,
          confidence: qualityAssessment.score,
          issues: [],
          suggestions: [],
          usedSecondLanguage: false
        };
      }

    } catch (error) {
      console.error(`${COLORS.ERROR}❌ [SecondLanguageValidator] Validation failed: ${error.message}${COLORS.RESET}`);
      return {
        isValid: true, // Default to valid on error to avoid blocking translation
        confidence: 0.5,
        issues: [`Validation error: ${error.message}`],
        suggestions: [],
        usedSecondLanguage: false,
        error: error.message
      };
    }
  }

  /**
   * Assess translation quality to determine if validation is needed
   * @param {string} originalText - Original English text
   * @param {string} polishTranslation - Polish translation
   * @param {Object} context - Translation context
   * @returns {Promise<Object>} - Quality assessment result
   */
  async assessTranslationQuality(originalText, polishTranslation, context) {
    const tools = [
      {
        name: "assess_translation_quality",
        description: "Assess the quality of a Polish translation and determine if second language validation is needed",
        input_schema: {
          type: "object",
          properties: {
            quality_score: {
              type: "number",
              minimum: 0,
              maximum: 1,
              description: "Overall quality score (0-1, where 1 is perfect)"
            },
            needs_validation: {
              type: "boolean",
              description: "Whether this translation needs second language validation"
            },
            issues: {
              type: "array",
              items: { type: "string" },
              description: "List of identified issues with the translation"
            },
            reason: {
              type: "string",
              description: "Main reason why validation is or isn't needed"
            },
            suggestions: {
              type: "array",
              items: { type: "string" },
              description: "Suggestions for improvement if issues are found"
            }
          },
          required: ["quality_score", "needs_validation", "reason"]
        }
      }
    ];

    const prompt = this.buildQualityAssessmentPrompt(originalText, polishTranslation, context);

    console.log(`${COLORS.INFO}📤 [SecondLanguageValidator] Sending quality assessment to Claude 4 Sonnet (${this.analysisModel})...${COLORS.RESET}`);
    const response = await this.anthropic.messages.create({
      model: this.analysisModel,
      max_tokens: 2048,
      temperature: 0.3,
      messages: [{ role: 'user', content: prompt }],
      tools: tools,
      tool_choice: { type: "tool", name: "assess_translation_quality" }
    });
    console.log(`${COLORS.SUCCESS}📥 [SecondLanguageValidator] Received quality assessment from Claude 4 Sonnet${COLORS.RESET}`);

    // Process the response
    for (const content of response.content) {
      if (content.type === 'tool_use' && content.name === 'assess_translation_quality') {
        console.log(`${COLORS.DEBUG}🔍 [SecondLanguageValidator] Processing quality assessment response:${COLORS.RESET}`);
        console.log(`${COLORS.DEBUG}📊 [SecondLanguageValidator] Input keys: ${Object.keys(content.input || {}).join(', ')}${COLORS.RESET}`);

        const input = content.input || {};
        return {
          score: typeof input.quality_score === 'number' ? input.quality_score : 0.7,
          needsValidation: input.needs_validation !== undefined ? input.needs_validation : false,
          reason: input.reason || "Quality assessment completed",
          issues: Array.isArray(input.issues) ? input.issues : [],
          suggestions: Array.isArray(input.suggestions) ? input.suggestions : []
        };
      }
    }

    // Fallback if tool use fails
    return {
      score: 0.7,
      needsValidation: false,
      reason: "Assessment tool failed, defaulting to acceptable quality",
      issues: [],
      suggestions: []
    };
  }

  /**
   * Find corresponding second language source file
   * @param {string} fileName - Original file name
   * @returns {Promise<string|null>} - Second language content or null
   */
  async findSecondLanguageSource(fileName) {
    if (!fileName) return null;
    
    try {
      // Convert from _eng.txt to _other.txt format
      const baseFileName = fileName.replace('_eng.txt', '');
      const secondLanguageFile = `${baseFileName}_other.txt`;
      const secondLanguagePath = path.join(this.secondLanguageDirectory, secondLanguageFile);
      
      if (fs.existsSync(secondLanguagePath)) {
        const content = fs.readFileSync(secondLanguagePath, 'utf8');
        console.log(`${COLORS.INFO}📄 [SecondLanguageValidator] Found second language source: ${secondLanguageFile}${COLORS.RESET}`);
        return content;
      }
      
      console.log(`${COLORS.DEBUG}🔍 [SecondLanguageValidator] No second language source found for: ${fileName}${COLORS.RESET}`);
      return null;
      
    } catch (error) {
      console.warn(`${COLORS.WARNING}⚠️  [SecondLanguageValidator] Error reading second language source: ${error.message}${COLORS.RESET}`);
      return null;
    }
  }

  /**
   * Perform cross-validation using second language source
   * @param {string} originalText - Original English text
   * @param {string} polishTranslation - Polish translation to validate
   * @param {string} secondLanguageText - Second language source text
   * @param {Object} context - Translation context
   * @returns {Promise<Object>} - Cross-validation result
   */
  async performCrossValidation(originalText, polishTranslation, secondLanguageText, context) {
    const tools = [
      {
        name: "cross_validate_translation",
        description: "Cross-validate Polish translation using second language source",
        input_schema: {
          type: "object",
          properties: {
            is_valid: {
              type: "boolean",
              description: "Whether the Polish translation is valid after cross-validation"
            },
            confidence: {
              type: "number",
              minimum: 0,
              maximum: 1,
              description: "Confidence in the validation result (0-1)"
            },
            issues_found: {
              type: "array",
              items: {
                type: "object",
                properties: {
                  description: { type: "string", description: "Description of the issue" },
                  priority: { type: "string", enum: ["low", "medium", "high"], description: "Priority level of the issue" },
                  category: { type: "string", enum: ["grammar", "naturalness", "accuracy", "cultural", "consistency"], description: "Category of the issue" }
                },
                required: ["description", "priority"]
              },
              description: "Issues found during cross-validation with priority levels"
            },
            improved_translation: {
              type: "string",
              description: "Improved Polish translation if issues were found"
            },
            validation_notes: {
              type: "string",
              description: "Notes about the validation process and findings"
            }
          },
          required: ["is_valid", "confidence"]
        }
      }
    ];

    const prompt = this.buildCrossValidationPrompt(originalText, polishTranslation, secondLanguageText, context);

    console.log(`${COLORS.INFO}📤 [SecondLanguageValidator] Sending cross-validation request to Claude 3.5 Sonnet (${this.generationModel})...${COLORS.RESET}`);
    const response = await this.anthropic.messages.create({
      model: this.generationModel,
      max_tokens: 3072,
      temperature: 0.4,
      messages: [{ role: 'user', content: prompt }],
      tools: tools,
      tool_choice: { type: "tool", name: "cross_validate_translation" }
    });
    console.log(`${COLORS.SUCCESS}📥 [SecondLanguageValidator] Received cross-validation result from Claude 3.5 Sonnet${COLORS.RESET}`);

    // Process the response
    for (const content of response.content) {
      if (content.type === 'tool_use' && content.name === 'cross_validate_translation') {
        console.log(`${COLORS.DEBUG}🔍 [SecondLanguageValidator] Processing cross-validation response:${COLORS.RESET}`);
        console.log(`${COLORS.DEBUG}📊 [SecondLanguageValidator] Input keys: ${Object.keys(content.input || {}).join(', ')}${COLORS.RESET}`);

        const input = content.input || {};

        // Log detailed cross-validation results
        console.log(`${COLORS.INFO}📋 [SecondLanguageValidator] Cross-validation results received:${COLORS.RESET}`);
        console.log(`${COLORS.INFO}   ✅ Is Valid: ${input.is_valid !== undefined ? input.is_valid : 'undefined'}${COLORS.RESET}`);
        console.log(`${COLORS.INFO}   📊 Confidence: ${typeof input.confidence === 'number' ? (input.confidence * 100).toFixed(1) + '%' : 'undefined'}${COLORS.RESET}`);
        console.log(`${COLORS.INFO}   🔍 Issues Found: ${Array.isArray(input.issues_found) ? input.issues_found.length : 0}${COLORS.RESET}`);

        if (Array.isArray(input.issues_found) && input.issues_found.length > 0) {
          console.log(`${COLORS.WARNING}⚠️  [SecondLanguageValidator] Issues identified:${COLORS.RESET}`);
          input.issues_found.forEach((issue, index) => {
            if (typeof issue === 'object' && issue.description) {
              const priorityColor = issue.priority === 'high' ? COLORS.ERROR :
                                   issue.priority === 'medium' ? COLORS.WARNING : COLORS.INFO;
              console.log(`${priorityColor}     ${index + 1}. [${issue.priority?.toUpperCase() || 'UNKNOWN'}] ${issue.description}${COLORS.RESET}`);
              if (issue.category) {
                console.log(`${COLORS.GRAY}        Category: ${issue.category}${COLORS.RESET}`);
              }
            } else {
              // Handle legacy string format
              console.log(`${COLORS.WARNING}     ${index + 1}. ${String(issue)}${COLORS.RESET}`);
            }
          });
        }

        if (input.improved_translation) {
          console.log(`${COLORS.SUCCESS}✨ [SecondLanguageValidator] Improved translation provided:${COLORS.RESET}`);
          console.log(`${COLORS.SUCCESS}${input.improved_translation.substring(0, 200)}${input.improved_translation.length > 200 ? '...' : ''}${COLORS.RESET}`);
        } else {
          console.log(`${COLORS.INFO}📝 [SecondLanguageValidator] No improved translation provided${COLORS.RESET}`);
        }

        if (input.validation_notes) {
          console.log(`${COLORS.INFO}📝 [SecondLanguageValidator] Validation notes: ${input.validation_notes}${COLORS.RESET}`);
        }

        return {
          isValid: input.is_valid !== undefined ? input.is_valid : true,
          confidence: typeof input.confidence === 'number' ? input.confidence : 0.5,
          issues: Array.isArray(input.issues_found) ? input.issues_found : [],
          improvedTranslation: input.improved_translation || null,
          suggestions: input.improved_translation ? [input.improved_translation] : [],
          validationNotes: input.validation_notes || null,
          usedSecondLanguage: true
        };
      }
    }

    // Fallback if tool use fails
    return {
      isValid: true,
      confidence: 0.5,
      issues: ["Cross-validation tool failed"],
      suggestions: [],
      usedSecondLanguage: false
    };
  }

  /**
   * Build cross-validation prompt
   * @param {string} originalText - Original English text
   * @param {string} polishTranslation - Polish translation
   * @param {string} secondLanguageText - Second language source
   * @param {Object} context - Translation context
   * @returns {string} - Cross-validation prompt
   */
  buildCrossValidationPrompt(originalText, polishTranslation, secondLanguageText, context) {
    return `You are an expert translator performing cross-validation and improvement generation. Use the second language source to validate and potentially improve the Polish translation.

ORIGINAL ENGLISH:
${originalText}

CURRENT POLISH TRANSLATION:
${polishTranslation}

SECOND LANGUAGE SOURCE (for reference):
${secondLanguageText}

CONTEXT:
- Anime: ${context.animeTitle || 'Unknown'}
- Episode: ${context.episode || 'Unknown'}
- Scene type: ${context.sceneType || 'Unknown'}

Cross-validation process:
1. Compare the Polish translation with both English and second language sources
2. Check if the Polish translation captures the meaning accurately
3. Identify any cultural nuances that might be better expressed
4. Look for grammatical or stylistic improvements
5. Ensure the translation sounds natural in Polish

When identifying issues, categorize them by priority:
- HIGH: Critical errors that significantly impact meaning, grammar, or naturalness
- MEDIUM: Noticeable issues that affect quality but don't break understanding
- LOW: Minor improvements that would enhance the translation

Issue categories:
- grammar: Polish grammar, syntax, case declension errors
- naturalness: Unnatural phrasing, awkward word order
- accuracy: Meaning preservation, context appropriateness
- cultural: Cultural adaptation, anime-specific references
- consistency: Character voice, terminology consistency

If issues are found, provide an improved Polish translation that:
- Maintains the exact number of lines
- Preserves speaker information and formatting
- Sounds natural and engaging in Polish
- Captures cultural nuances appropriately
- Follows Polish grammar and punctuation rules

Use the cross_validate_translation tool to provide your validation result.`;
  }

  /**
   * Build quality assessment prompt
   * @param {string} originalText - Original English text
   * @param {string} polishTranslation - Polish translation
   * @param {Object} context - Translation context
   * @returns {string} - Assessment prompt
   */
  buildQualityAssessmentPrompt(originalText, polishTranslation, context) {
    return `You are an expert Polish translation quality assessor. Analyze this translation and determine if it needs validation using a second language source.

ORIGINAL ENGLISH:
${originalText}

POLISH TRANSLATION:
${polishTranslation}

CONTEXT:
- Anime: ${context.animeTitle || 'Unknown'}
- Episode: ${context.episode || 'Unknown'}
- Scene type: ${context.sceneType || 'Unknown'}

Assess the translation for:
1. Grammatical correctness in Polish
2. Natural flow and readability
3. Accuracy to the original meaning
4. Cultural appropriateness for Polish anime audience
5. Consistency with Polish language conventions

Flag for second language validation if the translation:
- Sounds unnatural or awkward in Polish
- Has grammatical errors or incorrect case usage
- Seems to miss cultural nuances
- Contains literal translations that don't work in Polish
- Has unclear or confusing phrasing

Use the assess_translation_quality tool to provide your assessment.`;
  }

  /**
   * Check if validation result contains high priority suggestions
   * @param {Object} validationResult - Validation result object
   * @returns {boolean} - True if high priority suggestions exist
   */
  hasHighPrioritySuggestions(validationResult) {
    if (!validationResult || !Array.isArray(validationResult.issues)) {
      return false;
    }

    return validationResult.issues.some(issue => {
      if (typeof issue === 'object' && issue.priority) {
        return issue.priority === 'high';
      }
      // For legacy string format, assume medium priority
      return false;
    });
  }
}
